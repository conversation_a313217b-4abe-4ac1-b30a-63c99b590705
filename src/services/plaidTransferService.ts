import {
  TransferCreateRequest,
  TransferGetRequest,
  TransferType,
  TransferNetwork,
  ACHClass,
  TransferUserInRequest,
  TransferAuthorizationCreateRequest,
  TransferIntentCreateRequest,
  CountryCode,
  Products,
  TransferLedgerGetRequest,
  TransferLedgerDepositRequest
} from 'plaid';
import { plaidClient } from '../config/plaidConfig';
import { executeQuerySingle, executeUpdate, executeQuery } from '../utils/database';
import logger from '../utils/logger';
import { logTransactionEvent } from '../services/auditService';
// Removed unused imports

// Enhanced in-memory cache to prevent duplicate transfers with better tracking
const transferCache = new Map<string, {
  timestamp: number;
  transferId: string;
  authorizationId?: string;
  attempts: number;
}>();
const CACHE_DURATION = 30000; // 30 seconds

// Retry configuration for error resilience
const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  INITIAL_DELAY: 1000, // 1 second
  MAX_DELAY: 10000, // 10 seconds
  BACKOFF_MULTIPLIER: 2,
  RETRYABLE_ERRORS: ['RATE_LIMIT_EXCEEDED', 'TEMPORARY_ERROR', 'NETWORK_ERROR']
};

// Webhook configuration
const WEBHOOK_CONFIG = {
  BASE_URL: process.env.WEBHOOK_BASE_URL || process.env.APP_BASE_URL || 'https://your-app.com',
  WEBHOOK_PATH: '/api/plaid/webhook'
};

/**
 * Generate a robust idempotency key for transfers
 */
function generateIdempotencyKey(userId: number, amount: number, type: string, additionalData?: string): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 9);
  const dataHash = additionalData ? Buffer.from(additionalData).toString('base64').substring(0, 8) : '';
  return `${type}_${userId}_${amount}_${timestamp}_${randomSuffix}${dataHash}`;
}

/**
 * Get real user information from database
 */
async function getRealUserInfo(userId: number): Promise<{ legal_name: string; email_address: string }> {
  try {
    const user = await executeQuerySingle(
      'SELECT full_name, email FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (user) {
      return {
        legal_name: user.full_name || `User ${userId}`,
        email_address: user.email || `user${userId}@example.com`
      };
    }

    // Fallback to default values
    return {
      legal_name: `User ${userId}`,
      email_address: `user${userId}@example.com`
    };
  } catch (error) {
    logger.warn('Failed to fetch real user info, using defaults', { userId, error });
    return {
      legal_name: `User ${userId}`,
      email_address: `user${userId}@example.com`
    };
  }
}

/**
 * Sleep function for retry delays
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry wrapper with exponential backoff
 */
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  context: string,
  maxRetries: number = RETRY_CONFIG.MAX_RETRIES
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;

      // Check if error is retryable
      const isRetryable = RETRY_CONFIG.RETRYABLE_ERRORS.some(retryableError =>
        error.message?.includes(retryableError) ||
        error.response?.data?.error_code === 'RATE_LIMIT_EXCEEDED' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ETIMEDOUT'
      );

      if (!isRetryable || attempt === maxRetries) {
        logger.error(`${context} failed after ${attempt + 1} attempts`, {
          error: error.message || error,
          plaidError: error.response?.data,
          attempt: attempt + 1,
          maxRetries: maxRetries + 1
        });
        throw error;
      }

      const delay = Math.min(
        RETRY_CONFIG.INITIAL_DELAY * Math.pow(RETRY_CONFIG.BACKOFF_MULTIPLIER, attempt),
        RETRY_CONFIG.MAX_DELAY
      );

      logger.warn(`${context} failed, retrying in ${delay}ms`, {
        error: error.message || error,
        attempt: attempt + 1,
        maxRetries: maxRetries + 1,
        delay
      });

      await sleep(delay);
    }
  }

  throw lastError;
}

/**
 * Get webhook URL for transfer updates
 */
function getWebhookUrl(): string {
  return `${WEBHOOK_CONFIG.BASE_URL}${WEBHOOK_CONFIG.WEBHOOK_PATH}`;
}

/**
 * Interface for transfer authorization result
 */
export interface TransferAuthorizationResult {
  success: boolean;
  authorizationId?: string;
  message?: string;
  failureReason?: string;
  decision?: string;
  decisionRationale?: string;
}

/**
 * Create a staff transfer authorization for direct bank transfers
 * For staff payments, we use Transfer Intent pattern to transfer from business account to external bank
 *
 * @param type Transfer type (credit or debit)
 * @param amount Amount to transfer (as a number)
 * @param user User information for the transfer recipient
 * @param bankAccount Bank account details (routing, account number, type)
 * @param network Transfer network (ACH, RTP, etc.)
 * @param achClass ACH class for the transfer
 * @returns TransferAuthorizationResult with authorization ID if successful
 */
export async function createStaffTransferAuthorization(
  type: TransferType,
  amount: number,
  user: TransferUserInRequest,
  bankAccount: {
    account_number: string;
    routing_number: string;
    account_type: string;
  },
  network: TransferNetwork = TransferNetwork.Ach,
  achClass: ACHClass = ACHClass.Ppd
): Promise<TransferAuthorizationResult> {
  try {
    // Format amount to ensure exactly 2 decimal places as required by Plaid
    const formattedAmount = parseFloat(amount.toFixed(2)).toFixed(2);

    logger.info('Creating staff transfer intent for external bank transfer', {
      type,
      amount: formattedAmount,
      network,
      achClass,
      bankAccount: `****${bankAccount.account_number.slice(-4)}`
    });

    // Get business account credentials from environment
    const businessAccessToken = process.env.PLAID_BUSINESS_ACCESS_TOKEN;
    const businessAccountId = process.env.PLAID_BUSINESS_ACCOUNT_ID;

    if (!businessAccessToken || !businessAccountId) {
      return {
        success: false,
        message: 'Business account not configured for transfers. Please configure PLAID_BUSINESS_ACCESS_TOKEN and PLAID_BUSINESS_ACCOUNT_ID environment variables.',
        failureReason: 'BUSINESS_ACCOUNT_NOT_CONFIGURED'
      };
    }

    // Create the authorization request for Plaid transfer
    // Note: For external bank transfers in sandbox, we use standard authorization
    const authorizationRequest: TransferAuthorizationCreateRequest = {
      access_token: businessAccessToken,
      account_id: businessAccountId,
      type,
      network,
      amount: formattedAmount,
      ach_class: achClass,
      user
    };

    logger.info('Creating transfer authorization for external bank transfer', {
      businessAccountId,
      amount: formattedAmount,
      type,
      network,
      achClass,
      destinationBank: `****${bankAccount.account_number.slice(-4)}`
    });

    // Call Plaid API to create the authorization
    const authResponse = await plaidClient.transferAuthorizationCreate(authorizationRequest);
    const authorization = authResponse.data.authorization;

    // Log the authorization result
    logger.info('Staff transfer authorization created', {
      authorizationId: authorization.id,
      decision: authorization.decision
    });

    // Log the event in the audit log (skip if no valid user ID to avoid foreign key constraint)
    try {
      await logTransactionEvent(
        'staff_transfer_authorization_created',
        authorization.id,
        user.legal_name ? 1 : 1, // Use a default system user ID instead of 0
        {
          authorizationId: authorization.id,
          decision: authorization.decision,
          type,
          amount: formattedAmount,
          network,
          achClass,
          bankAccount: `****${bankAccount.account_number.slice(-4)}`,
          userLegalName: user.legal_name
        }
      );
    } catch (logError) {
      logger.warn('Failed to log authorization event, continuing with transfer', { logError });
    }

    // Check if the authorization was approved
    if (authorization.decision !== 'approved') {
      return {
        success: false,
        authorizationId: authorization.id,
        decision: authorization.decision,
        decisionRationale: authorization.decision_rationale?.code,
        message: `Staff transfer authorization not approved: ${authorization.decision_rationale?.description || 'Unknown reason'}`,
        failureReason: authorization.decision_rationale?.code
      };
    }

    return {
      success: true,
      authorizationId: authorization.id,
      decision: authorization.decision,
      message: 'Staff transfer authorization created successfully'
    };

  } catch (error: any) {
    logger.error('Error creating staff transfer authorization', {
      type,
      amount,
      error: error.message || error,
      plaidError: error.response?.data
    });

    // Handle specific Plaid errors
    if (error.response?.data?.error_code) {
      const plaidError = error.response.data;
      return {
        success: false,
        message: `Staff authorization failed: ${plaidError.error_message || 'Transfer authorization not available'}`,
        failureReason: plaidError.error_code
      };
    }

    return {
      success: false,
      message: 'Failed to create staff transfer authorization. Please try again.',
      failureReason: 'STAFF_AUTHORIZATION_ERROR'
    };
  }
}

/**
 * Create a transfer authorization using Plaid's /transfer/authorization/create endpoint
 * This is the first step in the transfer process and must be completed before creating a transfer
 *
 * @param accessToken Plaid access token for the account
 * @param accountId Plaid account ID
 * @param type Transfer type (credit or debit)
 * @param amount Amount to transfer (as a number)
 * @param user User information for the transfer recipient/sender
 * @param network Transfer network (ACH, RTP, etc.)
 * @param achClass ACH class for the transfer
 * @param userId Optional user ID for better logging and tracking
 * @param webhookUrl Optional webhook URL for real-time updates
 * @returns TransferAuthorizationResult with authorization ID if successful
 */
export async function createTransferAuthorization(
  accessToken: string,
  accountId: string,
  type: TransferType,
  amount: number,
  user: TransferUserInRequest,
  network: TransferNetwork = TransferNetwork.Ach,
  achClass: ACHClass = ACHClass.Web,
  userId?: number,
  webhookUrl?: string
): Promise<TransferAuthorizationResult> {
  const operation = async () => {
    // Format amount to ensure exactly 2 decimal places as required by Plaid
    const formattedAmount = parseFloat(amount.toFixed(2)).toFixed(2);

    // Enhanced logging with more context
    logger.info('Creating transfer authorization with enhanced tracking', {
      accountId,
      type,
      amount: formattedAmount,
      network,
      achClass,
      userId,
      userLegalName: user.legal_name,
      userEmail: user.email_address,
      webhookUrl: webhookUrl || 'default'
    });

    // Create the authorization request with optional webhook
    const authorizationRequest: TransferAuthorizationCreateRequest = {
      access_token: accessToken,
      account_id: accountId,
      type,
      network,
      amount: formattedAmount,
      ach_class: achClass,
      user,
      ...(webhookUrl && { webhook: webhookUrl })
    };

    // Call Plaid API to create the authorization
    const authResponse = await plaidClient.transferAuthorizationCreate(authorizationRequest);
    const authorization = authResponse.data.authorization;

    // Enhanced logging with authorizationId
    logger.info('Transfer authorization created successfully', {
      authorizationId: authorization.id,
      decision: authorization.decision,
      type,
      amount: formattedAmount,
      userId,
      accountId,
      network,
      achClass,
      decisionRationale: authorization.decision_rationale?.code
    });

    // Log the event in the audit log with enhanced metadata
    await logTransactionEvent(
      'transfer_authorization_created',
      authorization.id,
      userId || 0,
      {
        authorizationId: authorization.id,
        decision: authorization.decision,
        type,
        amount: formattedAmount,
        network,
        achClass,
        userId,
        userLegalName: user.legal_name,
        userEmail: user.email_address,
        accountId,
        webhookConfigured: !!webhookUrl,
        decisionRationale: authorization.decision_rationale?.code
      }
    );

    // Check if the authorization was approved
    if (authorization.decision !== 'approved') {
      logger.warn('Transfer authorization not approved', {
        authorizationId: authorization.id,
        decision: authorization.decision,
        decisionRationale: authorization.decision_rationale?.code,
        decisionDescription: authorization.decision_rationale?.description,
        userId,
        amount: formattedAmount
      });

      return {
        success: false,
        authorizationId: authorization.id,
        decision: authorization.decision,
        decisionRationale: authorization.decision_rationale?.code,
        message: `Transfer authorization not approved: ${authorization.decision_rationale?.description || 'Unknown reason'}`,
        failureReason: authorization.decision_rationale?.code
      };
    }

    return {
      success: true,
      authorizationId: authorization.id,
      decision: authorization.decision,
      message: 'Transfer authorization created successfully'
    };
  };

  try {
    return await retryWithBackoff(operation, 'Transfer Authorization Creation');
  } catch (error: any) {
    logger.error('Error creating transfer authorization after retries', {
      accountId,
      type,
      amount,
      userId,
      error: error.message || error,
      plaidError: error.response?.data
    });

    // Handle specific Plaid errors
    if (error.response?.data?.error_code) {
      const plaidError = error.response.data;
      return {
        success: false,
        message: `Authorization failed: ${plaidError.error_message || 'Transfer authorization not available'}`,
        failureReason: plaidError.error_code
      };
    }

    return {
      success: false,
      message: 'Failed to create transfer authorization. Please try again.',
      failureReason: 'AUTHORIZATION_ERROR'
    };
  }
}

export interface PlaidTransferResult {
  success: boolean;
  transferId?: string;
  status?: string;
  message?: string;
  failureReason?: string;
}

/**
 * Create a transfer using Plaid's /transfer/create endpoint
 * This function requires a prior authorization via createTransferAuthorization
 * 
 * @param accessToken Plaid access token for the account
 * @param accountId Plaid account ID
 * @param authorizationId Authorization ID from createTransferAuthorization
 * @param type Transfer type (credit or debit)
 * @param amount Amount to transfer (as a number)
 * @param description Description of the transfer
 * @param user User information for the transfer recipient/sender
 * @param metadata Additional metadata to track the transfer purpose
 * @param network Transfer network (ACH, RTP, etc.)
 * @param achClass ACH class for the transfer
 * @param idempotencyKey Optional idempotency key to prevent duplicate transfers
 * @returns PlaidTransferResult with transfer ID if successful
 */

export async function createACHDebitTransferFormobile(
  userId: number,
  amount: number,
  description: string,
  bankAccountId?: string
): Promise<PlaidTransferResult> {
  try {
    logger.info('Creating ACH debit transfer with enhanced tracking', {
      userId,
      amount,
      description,
      bankAccountId
    });

    // Get bank account information
    const bankAccount = await getBankAccountForTransfer(userId, bankAccountId);
    if (!bankAccount) {
      return {
        success: false,
        message: 'Bank account not found or not properly configured for transfers',
        failureReason: 'BANK_ACCOUNT_NOT_FOUND'
      };
    }

    // Get real user information from database
    const realUserInfo = await getRealUserInfo(userId);

    // Create user object for Plaid with real information
    const user: TransferUserInRequest = {
      legal_name: realUserInfo.legal_name,
      email_address: realUserInfo.email_address
    };

    // Generate webhook URL for real-time updates
    const webhookUrl = getWebhookUrl();

    // Step 1: Create transfer authorization with enhanced tracking
    const authResult = await createTransferAuthorization(
      bankAccount.plaid_access_token,
      bankAccount.plaid_account_id,
      TransferType.Debit,
      amount,
      user,
      TransferNetwork.Ach,
      ACHClass.Web,
      userId,
      webhookUrl
    );

    if (!authResult.success || !authResult.authorizationId) {
      // Check if it's a ledger balance issue and provide better error message
      const isLedgerError = authResult.message?.includes('insufficient available_balance in Plaid Ledger');

      if (isLedgerError) {
        // Log detailed information for ledger balance issues
        logger.warn('Ledger balance issue detected', { userId, amount, message: authResult.message });
      }

      const errorMessage = isLedgerError
        ? 'Insufficient funds in business account. Please add funds to your Plaid Ledger account.'
        : authResult.message || 'Transfer authorization failed';

      return {
        success: false,
        message: errorMessage,
        failureReason: authResult.failureReason || 'AUTHORIZATION_FAILED'
      };
    }

    // Step 2: Create the actual transfer with enhanced tracking
    // Truncate description to 15 characters as required by Plaid
    const truncatedDescription = description.length > 15 ? description.substring(0, 15) : description;

    // Generate unique event ID for correlation
    const plaidEventId = `ach_debit_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const transferResult = await createTransfer(
      bankAccount.plaid_access_token,
      bankAccount.plaid_account_id,
      authResult.authorizationId,
      TransferType.Debit,
      amount,
      truncatedDescription,
      user,
      {
        user_id: userId.toString(),
        bank_account_id: bankAccountId || 'primary',
        transfer_type: 'ach_debit',
        bank_name: bankAccount.bank_name,
        account_mask: bankAccount.account_mask
      },
      TransferNetwork.Ach,
      ACHClass.Web,
      undefined, // idempotencyKey will be auto-generated
      userId,
      webhookUrl,
      plaidEventId
    );

    if (!transferResult.success) {
      return {
        success: false,
        message: transferResult.message || 'Transfer creation failed',
        failureReason: transferResult.failureReason || 'TRANSFER_FAILED'
      };
    }

    // Create transaction record with plaid_event_id for proper correlation
    if (transferResult.transferId) {
      try {
        await executeUpdate(
          `INSERT INTO tbl_wallet_transactions
           (user_id, type, amount, reference_id, payment_provider, description, status_id, meta_data, plaid_event_id, created_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
          [
            userId,
            'debit',
            -Math.abs(amount), // Negative for debit
            transferResult.transferId,
            'plaid_ach_debit',
            description,
            2, // pending status
            JSON.stringify({
              transfer_type: 'ach_debit',
              bank_account_id: bankAccountId || 'primary',
              bank_name: bankAccount.bank_name,
              account_mask: bankAccount.account_mask,
              plaid_status: transferResult.status,
              authorization_id: authResult.authorizationId,
              user_legal_name: user.legal_name,
              user_email: user.email_address
            }),
            plaidEventId
          ]
        );

        logger.info('Transaction record created with plaid_event_id', {
          userId,
          transferId: transferResult.transferId,
          plaidEventId
        });
      } catch (dbError) {
        logger.error('Failed to create transaction record', {
          userId,
          transferId: transferResult.transferId,
          plaidEventId,
          error: dbError
        });
      }
    }

    logger.info('ACH debit transfer created successfully with enhanced tracking', {
      userId,
      amount,
      transferId: transferResult.transferId,
      status: transferResult.status,
      plaidEventId,
      authorizationId: authResult.authorizationId
    });

    return transferResult;

  } catch (error: any) {
    logger.error('Error creating ACH debit transfer', {
      userId,
      amount,
      description,
      bankAccountId,
      error: error.message || error
    });

    return {
      success: false,
      message: 'Failed to create ACH debit transfer. Please try again.',
      failureReason: 'TRANSFER_ERROR'
    };
  }
}
export async function createTransfer(
  accessToken: string,
  accountId: string,
  authorizationId: string,
  type: TransferType,
  amount: number,
  description: string,
  user: TransferUserInRequest,
  metadata: Record<string, string> = {},
  network: TransferNetwork = TransferNetwork.Ach,
  achClass: ACHClass = ACHClass.Web,
  idempotencyKey?: string,
  userId?: number,
  webhookUrl?: string,
  plaidEventId?: string
): Promise<PlaidTransferResult> {
  const operation = async () => {
    // Format amount to ensure exactly 2 decimal places as required by Plaid
    const formattedAmount = parseFloat(amount.toFixed(2)).toFixed(2);

    // Generate enhanced idempotency key if not provided
    const transferIdempotencyKey = idempotencyKey ||
      generateIdempotencyKey(userId || 0, amount, 'transfer', authorizationId);

    // Enhanced metadata with internal tracking
    const enhancedMetadata = {
      ...metadata,
      internal_transaction_id: transferIdempotencyKey,
      transfer_type: type,
      user_id: userId?.toString() || '0',
      authorization_id: authorizationId,
      network: network,
      ach_class: achClass,
      created_at: new Date().toISOString(),
      ...(plaidEventId && { plaid_event_id: plaidEventId })
    };

    // Enhanced logging with authorizationId and more context
    logger.info('Creating transfer with enhanced tracking', {
      accountId,
      authorizationId,
      amount: formattedAmount,
      idempotencyKey: transferIdempotencyKey,
      userId,
      type,
      network,
      achClass,
      userLegalName: user.legal_name,
      userEmail: user.email_address,
      metadataKeys: Object.keys(enhancedMetadata),
      webhookUrl: webhookUrl || 'default',
      plaidEventId
    });

    // Create the transfer request with enhanced metadata
    const transferRequest: TransferCreateRequest = {
      access_token: accessToken,
      account_id: accountId,
      authorization_id: authorizationId,
      amount: formattedAmount,
      description,
      metadata: enhancedMetadata,
      ...(webhookUrl && { webhook: webhookUrl })
    };

    // Call Plaid API to create the transfer
    const transferResponse = await plaidClient.transferCreate(transferRequest);
    const transfer = transferResponse.data.transfer;

    // Enhanced logging with comprehensive transfer details
    logger.info('Transfer created successfully with enhanced tracking', {
      transferId: transfer.id,
      authorizationId,
      status: transfer.status,
      amount: formattedAmount,
      type,
      network,
      achClass,
      userId,
      idempotencyKey: transferIdempotencyKey,
      created: transfer.created,
      webhookConfigured: !!webhookUrl,
      plaidEventId
    });

    // Log the event in the audit log with enhanced metadata
    await logTransactionEvent(
      'transfer_created',
      transfer.id,
      userId || 0,
      {
        transferId: transfer.id,
        authorizationId,
        status: transfer.status,
        amount: formattedAmount,
        type,
        network,
        achClass,
        userId,
        userLegalName: user.legal_name,
        userEmail: user.email_address,
        idempotencyKey: transferIdempotencyKey,
        metadata: enhancedMetadata,
        webhookConfigured: !!webhookUrl,
        plaidEventId
      }
    );

    return {
      success: true,
      transferId: transfer.id,
      status: transfer.status,
      message: `Transfer created successfully: ${formattedAmount}`
    };
  };

  try {
    return await retryWithBackoff(operation, 'Transfer Creation');
  } catch (error: any) {
    logger.error('Error creating transfer after retries', {
      accountId,
      authorizationId,
      amount,
      userId,
      error: error.message || error,
      plaidError: error.response?.data
    });

    // Handle specific Plaid errors
    if (error.response?.data?.error_code) {
      const plaidError = error.response.data;
      return {
        success: false,
        message: `Transfer failed: ${plaidError.error_message || 'Transfer creation not available'}`,
        failureReason: plaidError.error_code
      };
    }

    return {
      success: false,
      message: 'Failed to create transfer. Please try again.',
      failureReason: 'TRANSFER_ERROR'
    };
  }
}

export interface BankAccountInfo {
  id: number;
  plaid_access_token: string;
  plaid_account_id: string;
  bank_name: string;
  account_mask: string;
}

/**
 * Get bank account information with access token for transfers
 */
export async function getBankAccountForTransfer(userId: number, bankAccountId?: string): Promise<BankAccountInfo | null> {
  try {
    let query: string;
    let params: any[];

    if (bankAccountId) {
      // Get specific bank account by ID
      query = `SELECT id, plaid_access_token, plaid_account_id, bank_name, account_mask
               FROM tbl_bank_accounts
               WHERE user_id = ? AND id = ? AND plaid_access_token IS NOT NULL`;
      params = [userId, bankAccountId];
    } else {
      // Get primary bank account
      query = `SELECT id, plaid_access_token, plaid_account_id, bank_name, account_mask
               FROM tbl_bank_accounts
               WHERE user_id = ? AND is_primary = 1 AND plaid_access_token IS NOT NULL`;
      params = [userId];
    }

    const account = await executeQuerySingle(query, params);

    if (!account) {
      const accountType = bankAccountId ? 'selected bank account' : 'primary bank account';
      logger.warn(`No ${accountType} with access token found`, { userId, bankAccountId });
      return null;
    }

    return account as BankAccountInfo;
  } catch (error) {
    logger.error('Error getting bank account for transfer', { userId, bankAccountId, error });
    return null;
  }
}

/**
 * Get transfer status from Plaid
 */
export async function getTransferStatus(transferId: string): Promise<PlaidTransferResult> {
  try {
    // Check if this is a simulated transfer
    const transaction = await executeQuerySingle(
      'SELECT status_id, meta_data FROM tbl_wallet_transactions WHERE reference_id = ?',
      [transferId]
    );

    if (transaction) {
      const metaData = typeof transaction.meta_data === 'string'
        ? JSON.parse(transaction.meta_data)
        : transaction.meta_data;

      if (metaData?.simulated) {
        const status = transaction.status_id === 1 ? 'posted' :
          transaction.status_id === 2 ? 'pending' : 'failed';

        return {
          success: true,
          transferId: transferId,
          status: status,
          message: `Transfer status: ${status}`
        };
      }

      // For real transfers, check with Plaid API
      if (metaData?.real_transfer && !metaData?.simulated) {
        try {
          logger.info('Checking real transfer status with Plaid API', { transferId });

          // Create the transfer get request
          const transferGetRequest: TransferGetRequest = {
            transfer_id: transferId
          };

          // Call Plaid API to get transfer status
          const transferResponse = await plaidClient.transferGet(transferGetRequest);
          const transfer = transferResponse.data.transfer;

          // Log the transfer status
          logger.info('Retrieved transfer status from Plaid', {
            transferId,
            status: transfer.status,
            failureReason: transfer.failure_reason
          });

          // Update status in database if it has changed
          if (transfer.status !== metaData.plaid_status) {
            await updateTransferStatus(
              transferId,
              transfer.status,
              transfer.failure_reason?.description
            );

            // Log the status change
            await logTransactionEvent(
              'transfer_status_updated',
              transferId,
              transaction.user_id || 0,
              {
                oldStatus: metaData.plaid_status,
                newStatus: transfer.status,
                failureReason: transfer.failure_reason?.description
              }
            );
          }

          return {
            success: true,
            transferId: transferId,
            status: transfer.status,
            message: `Transfer status: ${transfer.status}`,
            failureReason: transfer.failure_reason?.description
          };
        } catch (plaidError: any) {
          logger.error('Error getting transfer status from Plaid API', {
            transferId,
            error: plaidError.message || plaidError,
            plaidError: plaidError.response?.data
          });

          // Fall back to database status if Plaid API call fails
          const status = transaction.status_id === 1 ? 'posted' :
            transaction.status_id === 2 ? 'pending' : 'failed';

          return {
            success: true,
            transferId: transferId,
            status: status,
            message: `Transfer status (from database): ${status}`
          };
        }
      }
    }

    // Fall back to database status
    const status = transaction?.status_id === 1 ? 'posted' :
      transaction?.status_id === 2 ? 'pending' : 'failed';

    return {
      success: true,
      transferId: transferId,
      status: status || 'unknown',
      message: `Transfer status: ${status || 'unknown'}`
    };
  } catch (error: any) {
    logger.error('Error getting transfer status', { transferId, error });

    return {
      success: false,
      message: 'Failed to get transfer status'
    };
  }
}

/**
 * Update transfer status in database
 */
export async function updateTransferStatus(
  transferId: string,
  status: string,
  failureReason?: string
): Promise<boolean> {
  try {
    // Update status in existing wallet_transactions table using reference_id (Plaid transfer ID)
    await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET status_id = ?,
           meta_data = JSON_SET(meta_data, '$.plaid_status', ?, '$.failure_reason', ?),
           last_updated = NOW()
       WHERE reference_id = ?`,
      [
        status === 'posted' ? 1 : (status === 'failed' ? 3 : 2), // 1=completed, 2=pending, 3=failed
        status,
        failureReason || null,
        transferId
      ]
    );

    logger.info('Transfer status updated', { transferId, status, failureReason });
    return true;

  } catch (error) {
    logger.error('Error updating transfer status', { transferId, status, error });
    return false;
  }
}

/**
 * Check if transfer is completed successfully
 */
export function isTransferCompleted(status: string): boolean {
  return status === 'posted' || status === 'settled';
}

/**
 * Check if transfer failed
 */
export function isTransferFailed(status: string): boolean {
  return status === 'failed' || status === 'cancelled' || status === 'returned';
}

/**
 * Poll for transfer status updates
 * This function will periodically check the status of a transfer until it reaches a terminal state
 * 
 * @param transferId The ID of the transfer to poll
 * @param callback Optional callback function to execute when status changes
 * @param interval Polling interval in milliseconds (default: 30000 ms = 30 seconds)
 * @param maxAttempts Maximum number of polling attempts (default: 20)
 * @returns Promise that resolves when polling is complete
 */
export async function pollTransferStatus(
  transferId: string,
  callback?: (status: PlaidTransferResult) => void,
  interval: number = 30000,
  maxAttempts: number = 20
): Promise<PlaidTransferResult> {
  let attempts = 0;
  let lastStatus: string | undefined;

  // Get initial status
  let statusResult = await getTransferStatus(transferId);

  // Execute callback with initial status if provided
  if (callback && statusResult.success) {
    callback(statusResult);
  }

  // Store initial status
  lastStatus = statusResult.status;

  // Check if status is already terminal
  if (isTransferCompleted(statusResult.status || '') || isTransferFailed(statusResult.status || '')) {
    return statusResult;
  }

  // Set up polling
  return new Promise((resolve) => {
    const poll = async () => {
      attempts++;

      try {
        // Get current status
        statusResult = await getTransferStatus(transferId);

        // Check if status has changed
        if (statusResult.status !== lastStatus) {
          logger.info('Transfer status changed during polling', {
            transferId,
            oldStatus: lastStatus,
            newStatus: statusResult.status
          });

          // Execute callback if provided
          if (callback && statusResult.success) {
            callback(statusResult);
          }

          // Update last status
          lastStatus = statusResult.status;
        }

        // Check if status is terminal or max attempts reached
        if (
          isTransferCompleted(statusResult.status || '') ||
          isTransferFailed(statusResult.status || '') ||
          attempts >= maxAttempts
        ) {
          // Resolve with final status
          resolve(statusResult);
        } else {
          // Continue polling
          setTimeout(poll, interval);
        }
      } catch (error) {
        logger.error('Error polling transfer status', { transferId, attempts, error });

        // Continue polling despite error
        if (attempts < maxAttempts) {
          setTimeout(poll, interval);
        } else {
          // Resolve with last known status after max attempts
          resolve(statusResult);
        }
      }
    };

    // Start polling after initial interval
    setTimeout(poll, interval);
  });
}

/**
 * Get user's pending transfers
 */
export async function getPendingTransfers(userId: number): Promise<any[]> {
  try {
    const transfers = await executeQuery(
      `SELECT id, user_id, amount, reference_id as transfer_id, description,
              status_id, created_at, meta_data
       FROM tbl_wallet_transactions
       WHERE user_id = ?
         AND payment_provider LIKE 'plaid_%'
         AND status_id = 2
       ORDER BY created_at DESC`,
      [userId]
    );

    return Array.isArray(transfers) ? transfers : (transfers ? [transfers] : []);

  } catch (error) {
    logger.error('Error getting pending transfers', { userId, error });
    return [];
  }
}

/**
 * Check and update status of pending transfers
 * This can be called periodically to sync transfer statuses
 */
export async function syncPendingTransfers(): Promise<void> {
  try {
    // Get all pending transfers from wallet_transactions table
    const pendingTransfers = await executeQuery(
      `SELECT reference_id as transfer_id FROM tbl_wallet_transactions
       WHERE payment_provider LIKE 'plaid_%'
         AND status_id = 2
         AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)`, // Only check transfers from last 7 days
      []
    );

    const transfers = Array.isArray(pendingTransfers) ? pendingTransfers : (pendingTransfers ? [pendingTransfers] : []);

    logger.info(`Syncing ${transfers.length} pending transfers`);

    for (const transfer of transfers) {
      try {
        const statusResult = await getTransferStatus(transfer.transfer_id);
        if (statusResult.success && statusResult.status) {
          await updateTransferStatus(transfer.transfer_id, statusResult.status);

          // Log status changes
          logger.info('Transfer status updated', {
            transferId: transfer.transfer_id,
            newStatus: statusResult.status
          });
        }
      } catch (error) {
        logger.error('Error syncing transfer status', {
          transferId: transfer.transfer_id,
          error
        });
      }
    }

  } catch (error) {
    logger.error('Error syncing pending transfers', { error });
  }
}/**
 * Create a staff bank transfer from organizer's wallet to staff's bank account
 * This function handles the complete flow of staff payments using real Plaid transfers
 *
 * @param organizerId Organizer's user ID
 * @param staffId Staff member's ID
 * @param amount Amount to transfer
 * @param description Transfer description
 * @param staffBankAccountId Staff's bank account ID
 * @param paymentMethodType Payment method type ('ach', 'same_day_ach', etc.)
 * @param pin Wallet PIN for verification (optional for bank transfers)
 * @param paymentSource Payment source ('wallet' or 'bank')
 * @param bankAccountId Organizer's bank account ID (for bank transfers)
 * @returns PlaidTransferResult with transfer details
 */
export async function createStaffBankTransfer(
  organizerId: number,
  staffId: number,
  amount: number,
  description: string,
  staffBankAccountId: string,
  paymentMethodType: string = 'ach',
  pin?: string,
  paymentSource: 'wallet' | 'bank' = 'wallet',
  bankAccountId?: string
): Promise<PlaidTransferResult & { transactionId?: number }> {
  try {
    logger.info('Creating staff bank transfer with enhanced tracking', {
      organizerId,
      staffId,
      amount,
      paymentSource,
      paymentMethodType,
      staffBankAccountId,
      description
    });

    // Format amount to ensure exactly 2 decimal places
    const transferAmount = parseFloat(amount.toFixed(2));

    // Step 1: Get staff bank account information first (needed for hold creation)
    const staffBankAccount = await executeQuerySingle(
      `SELECT id, staff_id, organizer_id, bank_name, account_number, routing_number,
              account_type, account_holder_name, status
       FROM tbl_staff_bank_accounts
       WHERE id = ? AND organizer_id = ? AND status = 'active'`,
      [staffBankAccountId, organizerId]
    );

    if (!staffBankAccount) {
      return {
        success: false,
        message: 'Staff bank account not found or inactive',
        failureReason: 'STAFF_BANK_ACCOUNT_NOT_FOUND'
      };
    }

    // Step 2: Verify payment source (wallet or bank) and create withdrawal hold if needed
    let withdrawalHoldId: number | undefined;

    if (paymentSource === 'wallet') {
      // Import wallet service functions and enhanced balance service
      const { getUserMasterWallet } = await import('./walletService');
      const { comparePin } = await import('../models/user');
      const { getEnhancedWalletBalance, createWithdrawalHold } = await import('./enhancedPendingBalanceService');

      // Verify wallet and PIN
      const wallet = await getUserMasterWallet(organizerId);
      if (!wallet) {
        return {
          success: false,
          message: 'Wallet not found',
          failureReason: 'WALLET_NOT_FOUND'
        };
      }

      if (!wallet.wallet_master_pin) {
        return {
          success: false,
          message: 'Wallet PIN not set',
          failureReason: 'PIN_NOT_SET'
        };
      }

      if (!pin) {
        return {
          success: false,
          message: 'PIN is required for wallet transfers',
          failureReason: 'PIN_REQUIRED'
        };
      }

      const isPinValid = await comparePin(pin, wallet.wallet_master_pin);
      if (!isPinValid) {
        return {
          success: false,
          message: 'Invalid wallet PIN',
          failureReason: 'INVALID_PIN'
        };
      }

      // Check available balance instead of main balance
      const balanceInfo = await getEnhancedWalletBalance(organizerId);
      if (!balanceInfo) {
        return {
          success: false,
          message: 'Failed to check wallet balance',
          failureReason: 'BALANCE_CHECK_FAILED'
        };
      }

      if (balanceInfo.available_balance < transferAmount) {
        return {
          success: false,
          message: `Insufficient wallet balance. Available: ${balanceInfo.available_balance.toFixed(2)}, Required: ${transferAmount.toFixed(2)}`,
          failureReason: 'INSUFFICIENT_BALANCE'
        };
      }

      // Create withdrawal hold BEFORE attempting Plaid transfer
      const tempTransferId = `temp_staff_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const holdResult = await createWithdrawalHold(
        organizerId,
        transferAmount,
        tempTransferId,
        `Staff payment to ${staffBankAccount.account_holder_name}`,
        {
          staffId: staffId,
          staffName: staffBankAccount.account_holder_name,
          staffBankAccountId: staffBankAccountId,
          paymentMethodType: paymentMethodType,
          transferType: 'staff_payment'
        }
      );

      if (!holdResult.success) {
        return {
          success: false,
          message: holdResult.message || 'Failed to create withdrawal hold',
          failureReason: 'HOLD_CREATION_FAILED'
        };
      }

      withdrawalHoldId = holdResult.holdId;
      logger.info('Withdrawal hold created successfully before Plaid transfer', {
        organizerId,
        transferAmount,
        holdId: withdrawalHoldId,
        tempTransferId
      });
    }

    // Step 3: Staff bank account already retrieved above

    // Step 4: Use your Plaid business account configuration
    // Only access_token and account_id are required for transfers
    const businessAccessToken = process.env.PLAID_BUSINESS_ACCESS_TOKEN;
    const businessAccountId = process.env.PLAID_BUSINESS_ACCOUNT_ID;

    if (!businessAccessToken || !businessAccountId) {
      return {
        success: false,
        message: 'Business account not configured for transfers. Please configure PLAID_BUSINESS_ACCESS_TOKEN and PLAID_BUSINESS_ACCOUNT_ID environment variables.',
        failureReason: 'BUSINESS_ACCOUNT_NOT_CONFIGURED'
      };
    }

    // Step 5: Get real staff user details for Plaid transfer
    // Fixed: Use correct query to get staff details from tbl_users table
    const staffUserDetails = await executeQuerySingle(
      `SELECT u.email, u.full_name, u.full_name as staff_name
       FROM tbl_users u
       WHERE u.id = ?`,
      [staffId]
    );

    const user = {
      legal_name: staffUserDetails?.full_name || staffUserDetails?.staff_name || staffBankAccount.account_holder_name,
      email_address: staffUserDetails?.email || `staff${staffId}@organization.com`
    };

    logger.info('Using real staff details for Plaid transfer', {
      staffId,
      legalName: user.legal_name,
      email: user.email_address
    });

    // Step 6: Check Plaid Ledger balance before attempting transfer
    const { checkPlaidLedgerFunds } = await import('../utils/plaidLedgerUtils');
    const fundsCheck = await checkPlaidLedgerFunds(transferAmount);
    
    if (!fundsCheck.sufficient) {
      // If wallet was debited, reverse it
      if (paymentSource === 'wallet') {
        const { getUserMasterWallet, updateWalletBalance } = await import('./walletService');
        const wallet = await getUserMasterWallet(organizerId);
        if (wallet) {
          const currentBalance = parseFloat(wallet.balance.toString());
          await updateWalletBalance(organizerId, currentBalance + transferAmount);
        }
      }

      return {
        success: false,
        message: `Transfer cannot be processed: ${fundsCheck.message}. Please contact support to fund the Plaid Ledger.`,
        failureReason: 'INSUFFICIENT_PLAID_LEDGER_FUNDS'
      };
    }

    // Step 6: Create transfer authorization for direct bank transfer (Case 2: Plaid-to-Bank)
    // Use destination object with staff's bank account details
    const authResult = await createStaffTransferAuthorization(
      TransferType.Credit,
      transferAmount,
      user,
      {
        account_number: staffBankAccount.account_number,
        routing_number: staffBankAccount.routing_number,
        account_type: staffBankAccount.account_type
      },
      TransferNetwork.Ach,
      paymentMethodType === 'same_day_ach' ? ACHClass.Web : ACHClass.Ppd
    );

    if (!authResult.success || !authResult.authorizationId) {
      // If wallet was debited, reverse it
      if (paymentSource === 'wallet') {
        const { getUserMasterWallet, updateWalletBalance } = await import('./walletService');
        const wallet = await getUserMasterWallet(organizerId);
        if (wallet) {
          const currentBalance = parseFloat(wallet.balance.toString());
          await updateWalletBalance(organizerId, currentBalance + transferAmount);
        }
      }

      return {
        success: false,
        message: authResult.message || 'Transfer authorization failed',
        failureReason: authResult.failureReason || 'AUTHORIZATION_FAILED'
      };
    }

    // Step 7: Create the actual transfer using Case 2: Plaid-to-Bank pattern
    const truncatedDescription = description.length > 15 ? description.substring(0, 15) : description;

    // Format amount to ensure exactly 2 decimal places as required by Plaid
    const formattedAmount = parseFloat(transferAmount.toFixed(2)).toFixed(2);

    // Generate idempotency key to prevent duplicate transfers
    const transferIdempotencyKey = `staff_transfer_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    logger.info('Creating staff transfer with funding account and destination', {
      fundingAccountId: businessAccountId,
      amount: formattedAmount,
      staffBankAccount: `****${staffBankAccount.account_number.slice(-4)}`,
      idempotencyKey: transferIdempotencyKey
    });

    // Create the staff transfer request for business-to-external-bank transfer
    // Note: Use simplified transfer request format (Plaid API updated)
    // Description must be 15 characters or less
    const shortDescription = truncatedDescription.length > 15
      ? truncatedDescription.substring(0, 15)
      : truncatedDescription;

    const transferRequest: TransferCreateRequest = {
      access_token: businessAccessToken,
      account_id: businessAccountId,
      authorization_id: authResult.authorizationId,
      amount: formattedAmount,
      description: shortDescription
    };

    // Call Plaid API to create the transfer from business account to staff bank account
    logger.info('Calling Plaid transferCreate API', {
      requestSummary: {
        idempotencyKey: transferIdempotencyKey,
        accountId: businessAccountId,
        authorizationId: authResult.authorizationId,
        type: TransferType.Credit,
        network: TransferNetwork.Ach,
        amount: formattedAmount,
        achClass: paymentMethodType === 'same_day_ach' ? ACHClass.Web : ACHClass.Ppd
      }
    });

    let transferResponse;
    let transfer;

    try {
      transferResponse = await plaidClient.transferCreate(transferRequest);
      transfer = transferResponse.data.transfer;
    } catch (transferError: any) {
      logger.error('Plaid transferCreate API failed', {
        error: transferError.message || transferError,
        plaidError: transferError.response?.data,
        requestSummary: {
          idempotencyKey: transferIdempotencyKey,
          accountId: businessAccountId,
          authorizationId: authResult.authorizationId,
          amount: formattedAmount
        }
      });

      // Release the withdrawal hold if it was created
      if (paymentSource === 'wallet' && withdrawalHoldId) {
        try {
          const { releaseFailedHold } = await import('./enhancedPendingBalanceService');
          await releaseFailedHold(`temp_staff_${Date.now()}`, 'Plaid transfer creation failed');
          logger.info('Released withdrawal hold due to transfer creation failure', { holdId: withdrawalHoldId });
        } catch (releaseError) {
          logger.error('Failed to release withdrawal hold after transfer failure', { holdId: withdrawalHoldId, releaseError });
        }
      }

      // Handle specific Plaid errors
      if (transferError.response?.data?.error_code) {
        const plaidError = transferError.response.data;
        return {
          success: false,
          message: `Transfer failed: ${plaidError.error_message || 'Transfer creation not available'}`,
          failureReason: plaidError.error_code
        };
      }

      return {
        success: false,
        message: 'Failed to create transfer with Plaid. Please try again.',
        failureReason: 'PLAID_TRANSFER_ERROR'
      };
    }

    logger.info('Staff transfer created successfully', {
      transferId: transfer.id,
      status: transfer.status,
      amount: formattedAmount,
      staffBankAccount: `****${staffBankAccount.account_number.slice(-4)}`
    });

    const transferResult = {
      success: true,
      transferId: transfer.id,
      status: transfer.status,
      message: `Staff transfer created successfully: $${formattedAmount}`
    };

    // Remove the incorrect logic check since transferResult.success is always true at this point
    // The transfer was successful if we reach here

    // Step 8: Update withdrawal hold with actual Plaid transfer ID
    if (paymentSource === 'wallet' && withdrawalHoldId) {
      // Update the hold record with the actual Plaid transfer ID
      await executeUpdate(
        `UPDATE tbl_pending_holds
         SET plaid_transfer_id = ?, reference_id = ?, updated_at = NOW()
         WHERE id = ?`,
        [transfer.id, transfer.id, withdrawalHoldId]
      );

      logger.info('Updated withdrawal hold with actual Plaid transfer ID', {
        holdId: withdrawalHoldId,
        plaidTransferId: transfer.id
      });
    }

    // Step 9: Get staff member's primary user ID from tbl_users
    const staffUser = await executeQuerySingle(
      'SELECT id FROM tbl_users WHERE id = ?',
      [staffId]
    );

    if (!staffUser) {
      logger.error('Staff user not found in tbl_users', { staffId });
      return {
        success: false,
        message: 'Staff user account not found',
        failureReason: 'STAFF_USER_NOT_FOUND'
      };
    }

    const staffUserId = staffUser.id;
    logger.info('Found staff user ID', { staffId, staffUserId });

    // Step 10: Create transaction records for both organizer and staff

    // Create transaction record for organizer (debit - money going out)
    const organizerTransactionResult = await executeUpdate(
      `INSERT INTO tbl_wallet_transactions
       (user_id, type, amount, reference_id, payment_provider, description, status_id, meta_data, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        organizerId,
        'debit',
        -Math.abs(transferAmount), // Negative value for debit
        transferResult.transferId,
        'plaid_staff_transfer',
        `Payment to ${staffBankAccount.account_holder_name}`,
        2, // pending status
        JSON.stringify({
          staffId: staffId,
          staffUserId: staffUserId,
          staffName: staffBankAccount.account_holder_name,
          bankAccount: `****${staffBankAccount.account_number.slice(-4)}`,
          bankName: staffBankAccount.bank_name,
          transferType: 'staff_payment_sent',
          paymentSource: paymentSource,
          paymentMethodType: paymentMethodType,
          plaid_status: transferResult.status,
          plaid_transfer_id: transferResult.transferId,
          transaction_pair: 'organizer_debit'
        })
      ]
    );

    // Create transaction record for staff member (credit - money coming in)
    const staffTransactionResult = await executeUpdate(
      `INSERT INTO tbl_wallet_transactions
       (user_id, type, amount, reference_id, payment_provider, description, status_id, meta_data, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        staffUserId, // Use the staff member's primary user ID
        'credit',
        transferAmount,
        transferResult.transferId,
        'plaid_staff_transfer',
        description,
        2, // pending status
        JSON.stringify({
          organizerId: organizerId,
          organizerName: 'Organization',
          staffId: staffId,
          bankAccount: `****${staffBankAccount.account_number.slice(-4)}`,
          bankName: staffBankAccount.bank_name,
          accountType: staffBankAccount.account_type,
          accountHolderName: staffBankAccount.account_holder_name,
          transferType: 'staff_payment_received',
          paymentSource: paymentSource,
          paymentMethodType: paymentMethodType,
          plaid_status: transferResult.status,
          plaid_transfer_id: transferResult.transferId,
          transaction_pair: 'staff_credit'
        })
      ]
    );

    // Use the organizer transaction for the main response
    const transactionResult = organizerTransactionResult;

    // Log the successful transfer
    await logTransactionEvent(
      'staff_transfer_created',
      transferResult.transferId || '',
      organizerId,
      {
        staffId,
        amount: transferAmount,
        paymentSource,
        paymentMethodType,
        transferId: transferResult.transferId,
        status: transferResult.status
      }
    );

    logger.info('Staff bank transfer created successfully', {
      organizerId,
      staffId,
      amount: transferAmount,
      transferId: transferResult.transferId,
      status: transferResult.status,
      paymentSource,
      transactionId: transactionResult.insertId
    });

    return {
      success: true,
      transferId: transferResult.transferId,
      status: transferResult.status,
      message: `Transfer of $${transferAmount.toFixed(2)} to ${staffBankAccount.account_holder_name} initiated successfully`,
      transactionId: transactionResult.insertId
    };

  } catch (error: any) {
    logger.error('Error creating staff bank transfer', {
      organizerId,
      staffId,
      amount,
      error: error.message || error
    });

    // If wallet was debited, attempt to reverse it
    if (paymentSource === 'wallet') {
      try {
        const { getUserMasterWallet, updateWalletBalance } = await import('./walletService');
        const wallet = await getUserMasterWallet(organizerId);
        if (wallet) {
          const currentBalance = parseFloat(wallet.balance.toString());
          await updateWalletBalance(organizerId, currentBalance + parseFloat(amount.toFixed(2)));
          logger.info('Wallet balance reversed due to transfer failure', { organizerId, amount });
        }
      } catch (reverseError) {
        logger.error('Failed to reverse wallet balance after transfer failure', { organizerId, amount, reverseError });
      }
    }

    return {
      success: false,
      message: 'Failed to create staff bank transfer. Please try again.',
      failureReason: 'TRANSFER_ERROR'
    };
  }
}

/**
 * Get transfer details by ID from Plaid
 * @param transferId The ID of the transfer to retrieve
 * @returns Transfer details or null if not found
 */
export async function getTransferById(transferId: string): Promise<any | null> {
  try {
    logger.info('Getting transfer details from Plaid', { transferId });

    // Create the transfer get request
    const transferGetRequest: TransferGetRequest = {
      transfer_id: transferId
    };

    // Call Plaid API to get transfer details
    const transferResponse = await plaidClient.transferGet(transferGetRequest);
    const transfer = transferResponse.data.transfer;

    logger.info('Retrieved transfer details from Plaid', {
      transferId,
      status: transfer.status,
      amount: transfer.amount,
      type: transfer.type
    });

    return transfer;

  } catch (error: any) {
    logger.error('Error getting transfer details from Plaid', {
      transferId,
      error: error.message || error,
      plaidError: error.response?.data
    });

    return null;
  }
}

/**
 * Create an ACH debit transfer (pull money from user's bank account)
 * This is a high-level function that handles the complete flow of authorization and transfer creation
 *
 * @param userId User ID for the transfer
 * @param amount Amount to transfer (as a number)
 * @param description Description of the transfer
 * @param bankAccountId Bank account ID (optional, uses primary if not provided)
 * @returns PlaidTransferResult with transfer ID if successful
 */
export async function createACHDebitTransfer(
  userId: number,
  amount: number,
  description: string,
  bankAccountId?: string
): Promise<PlaidTransferResult> {
  try {
    logger.info('Creating ACH debit transfer', { userId, amount, description, bankAccountId });

    // Get bank account information
    const bankAccount = await getBankAccountForTransfer(userId, bankAccountId);
    if (!bankAccount) {
      return {
        success: false,
        message: 'Bank account not found or not properly configured for transfers',
        failureReason: 'BANK_ACCOUNT_NOT_FOUND'
      };
    }

    // Create user object for Plaid
    const user: TransferUserInRequest = {
      legal_name: `User ${userId}`, // This should ideally come from user profile
      email_address: `user${userId}@example.com` // This should ideally come from user profile
    };

    // Step 1: Create transfer authorization
    const authResult = await createTransferAuthorization(
      bankAccount.plaid_access_token,
      bankAccount.plaid_account_id,
      TransferType.Debit,
      amount,
      user,
      TransferNetwork.Ach,
      ACHClass.Web
    );

    if (!authResult.success || !authResult.authorizationId) {
      // Check if it's a ledger balance issue and provide better error message
      const isLedgerError = authResult.message?.includes('insufficient available_balance in Plaid Ledger');

      if (isLedgerError) {
        // Log detailed information for ledger balance issues
        logger.warn('Ledger balance issue detected', { userId, amount, message: authResult.message });
      }

      const errorMessage = isLedgerError
        ? 'Insufficient funds in business account. Please add funds to your Plaid Ledger account.'
        : authResult.message || 'Transfer authorization failed';

      return {
        success: false,
        message: errorMessage,
        failureReason: authResult.failureReason || 'AUTHORIZATION_FAILED'
      };
    }

    // Step 2: Create the actual transfer
    // Truncate description to 15 characters as required by Plaid
    const truncatedDescription = description.length > 15 ? description.substring(0, 15) : description;

    const transferResult = await createTransfer(
      bankAccount.plaid_access_token,
      bankAccount.plaid_account_id,
      authResult.authorizationId,
      TransferType.Debit,
      amount,
      truncatedDescription,
      user,
      {
        user_id: userId.toString(),
        bank_account_id: bankAccountId || 'primary',
        transfer_type: 'ach_debit'
      },
      TransferNetwork.Ach,
      ACHClass.Web
    );

    if (!transferResult.success) {
      return {
        success: false,
        message: transferResult.message || 'Transfer creation failed',
        failureReason: transferResult.failureReason || 'TRANSFER_FAILED'
      };
    }

    logger.info('ACH debit transfer created successfully', {
      userId,
      amount,
      transferId: transferResult.transferId,
      status: transferResult.status
    });

    return transferResult;

  } catch (error: any) {
    logger.error('Error creating ACH debit transfer', {
      userId,
      amount,
      description,
      bankAccountId,
      error: error.message || error
    });

    return {
      success: false,
      message: 'Failed to create ACH debit transfer. Please try again.',
      failureReason: 'TRANSFER_ERROR'
    };
  }
}

/**
 * Create an ACH credit transfer (send money to user's bank account)
 * This is a high-level function that handles the complete flow of authorization and transfer creation
 *
 * @param userId User ID for the transfer
 * @param amount Amount to transfer (as a number)
 * @param description Description of the transfer
 * @param bankAccountId Bank account ID (optional, uses primary if not provided)
 * @param paymentMethodType Payment method type ('ach', 'wire', 'instant')
 * @returns PlaidTransferResult with transfer ID if successful
 */
export async function createACHCreditTransfer(
  userId: number,
  amount: number,
  description: string,
  bankAccountId?: string,
  paymentMethodType: string = 'ach'
): Promise<PlaidTransferResult> {
  try {
    logger.info('Creating ACH credit transfer with enhanced tracking', {
      userId,
      amount,
      description,
      bankAccountId,
      paymentMethodType
    });

    // Get bank account information
    const bankAccount = await getBankAccountForTransfer(userId, bankAccountId);
    if (!bankAccount) {
      return {
        success: false,
        message: 'Bank account not found or not properly configured for transfers',
        failureReason: 'BANK_ACCOUNT_NOT_FOUND'
      };
    }

    // Get real user information from database
    const realUserInfo = await getRealUserInfo(userId);

    // Create user object for Plaid with real information
    const user: TransferUserInRequest = {
      legal_name: realUserInfo.legal_name,
      email_address: realUserInfo.email_address
    };

    // Determine transfer network and ACH class based on payment method
    let network = TransferNetwork.Ach;
    let achClass = ACHClass.Web;

    if (paymentMethodType === 'instant') {
      // For instant transfers, we might use different settings
      achClass = ACHClass.Web; // Keep as Web for now
    } else if (paymentMethodType === 'wire') {
      // Wire transfers would use different network if supported
      network = TransferNetwork.Ach; // Keep as ACH for now
    }

    // Generate webhook URL for real-time updates
    const webhookUrl = getWebhookUrl();

    // Step 1: Create transfer authorization with enhanced tracking
    const authResult = await createTransferAuthorization(
      bankAccount.plaid_access_token,
      bankAccount.plaid_account_id,
      TransferType.Credit,
      amount,
      user,
      network,
      achClass,
      userId,
      webhookUrl
    );

    if (!authResult.success || !authResult.authorizationId) {
      // Check if it's a ledger balance issue and provide better error message
      const isLedgerError = authResult.message?.includes('insufficient available_balance in Plaid Ledger');

      if (isLedgerError) {
        // Log detailed information for ledger balance issues
        logger.warn('Ledger balance issue detected', { userId, amount, message: authResult.message });
      }

      const errorMessage = isLedgerError
        ? 'Insufficient funds in business account. Please add funds to your Plaid Ledger account.'
        : authResult.message || 'Transfer authorization failed';

      return {
        success: false,
        message: errorMessage,
        failureReason: authResult.failureReason || 'AUTHORIZATION_FAILED'
      };
    }

    // Step 2: Create the actual transfer with enhanced tracking
    // Truncate description to 15 characters as required by Plaid
    const truncatedDescription = description.length > 15 ? description.substring(0, 15) : description;

    // Generate unique event ID for correlation
    const plaidEventId = `ach_credit_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const transferResult = await createTransfer(
      bankAccount.plaid_access_token,
      bankAccount.plaid_account_id,
      authResult.authorizationId,
      TransferType.Credit,
      amount,
      truncatedDescription,
      user,
      {
        user_id: userId.toString(),
        bank_account_id: bankAccountId || 'primary',
        transfer_type: 'ach_credit',
        payment_method_type: paymentMethodType,
        bank_name: bankAccount.bank_name,
        account_mask: bankAccount.account_mask
      },
      network,
      achClass,
      undefined, // idempotencyKey will be auto-generated
      userId,
      webhookUrl,
      plaidEventId
    );

    if (!transferResult.success) {
      return {
        success: false,
        message: transferResult.message || 'Transfer creation failed',
        failureReason: transferResult.failureReason || 'TRANSFER_FAILED'
      };
    }

    // Create transaction record with plaid_event_id for proper correlation
    if (transferResult.transferId) {
      try {
        await executeUpdate(
          `INSERT INTO tbl_wallet_transactions
           (user_id, type, amount, reference_id, payment_provider, description, status_id, meta_data, plaid_event_id, created_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
          [
            userId,
            'credit',
            Math.abs(amount), // Positive for credit
            transferResult.transferId,
            'plaid_ach_credit',
            description,
            2, // pending status
            JSON.stringify({
              transfer_type: 'ach_credit',
              bank_account_id: bankAccountId || 'primary',
              bank_name: bankAccount.bank_name,
              account_mask: bankAccount.account_mask,
              plaid_status: transferResult.status,
              authorization_id: authResult.authorizationId,
              user_legal_name: user.legal_name,
              user_email: user.email_address,
              payment_method_type: paymentMethodType
            }),
            plaidEventId
          ]
        );

        logger.info('Transaction record created with plaid_event_id', {
          userId,
          transferId: transferResult.transferId,
          plaidEventId
        });
      } catch (dbError) {
        logger.error('Failed to create transaction record', {
          userId,
          transferId: transferResult.transferId,
          plaidEventId,
          error: dbError
        });
      }
    }

    logger.info('ACH credit transfer created successfully with enhanced tracking', {
      userId,
      amount,
      transferId: transferResult.transferId,
      status: transferResult.status,
      paymentMethodType,
      plaidEventId,
      authorizationId: authResult.authorizationId
    });

    return transferResult;

  } catch (error: any) {
    logger.error('Error creating ACH credit transfer', {
      userId,
      amount,
      description,
      bankAccountId,
      paymentMethodType,
      error: error.message || error
    });

    return {
      success: false,
      message: 'Failed to create ACH credit transfer. Please try again.',
      failureReason: 'TRANSFER_ERROR'
    };
  }
}

/**
 * Get Plaid Ledger balance
 * This function checks the current balance in the Plaid Ledger
 *
 * @returns Promise with ledger balance information
 */
export async function getPlaidLedgerBalance(): Promise<{ success: boolean; balance?: string; message?: string }> {
  try {
    logger.info('Getting Plaid Ledger balance');

    const request: TransferLedgerGetRequest = {};
    const response = await plaidClient.transferLedgerGet(request);

    // Handle the response structure safely
    const balanceData = response.data.balance;
    const balanceAmount = typeof balanceData === 'object' ? balanceData.available : balanceData || '0.00';
    const currency = 'USD'; // Default currency

    logger.info('Plaid Ledger balance retrieved', {
      balance: balanceAmount,
      currency: currency
    });

    return {
      success: true,
      balance: balanceAmount.toString(),
      message: `Ledger balance: ${balanceAmount} ${currency}`
    };

  } catch (error: any) {
    logger.error('Error getting Plaid Ledger balance', {
      error: error.message || error,
      plaidError: error.response?.data
    });

    return {
      success: false,
      message: 'Failed to get Plaid Ledger balance'
    };
  }
}

/**
 * Check if error is due to insufficient Plaid Ledger balance
 * @param error The error object from Plaid API
 * @returns boolean indicating if it's a ledger balance issue
 */
export function isLedgerBalanceError(error: any): boolean {
  const errorMessage = error?.response?.data?.error_message || error?.message || '';
  return errorMessage.includes('insufficient available_balance in Plaid Ledger') ||
    errorMessage.includes('INSUFFICIENT_FUNDS');
}

/**
 * Get user-friendly error message for ledger balance issues
 * @param originalError The original error message
 * @returns User-friendly error message with instructions
 */
export function getLedgerBalanceErrorMessage(originalError: string): string {
  return 'Insufficient funds in business account. Please add funds to your Plaid Ledger account.';
}